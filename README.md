# 万能授权码计算器 - JavaScript 加密版

## ✅ 问题解决

**原问题**: JavaScript 版本和 PHP 版本计算结果不一致

**解决方案**: 
1. 修正了哈希算法实现，确保使用标准的 SHA1 和 MD5 算法
2. 创建了 `calculate-fixed.js` 修正版本，与 PHP 版本结果完全一致
3. 提供了浏览器版本 `calculate-browser.js`，使用 crypto-js 库

**验证结果**:
- 手机号 `18701879170` → 授权码 `1751CB73E9` ✅
- 手机号 `13800138000` → 授权码 `A96B62FC7C` ✅  
- 手机号 `15912345678` → 授权码 `5F9AC24954` ✅

## 📁 文件说明

### 🔐 核心加密模块
1. **`calculate-fixed.js`** - 修正版加密模块（推荐使用）
   - 与 PHP 版本结果完全一致
   - 支持 Node.js 环境
   - 高度代码混淆和反调试保护

2. **`calculate-browser.js`** - 浏览器专用版本
   - 使用 crypto-js 库
   - 支持浏览器环境
   - 需要引入 crypto-js CDN

3. **`calculate-encrypted.js`** - 原始加密版本（已弃用）

### 🌐 使用示例
4. **`usage-example.html`** - 浏览器端完整示例
5. **`server.js`** - Node.js 服务器版本

### 🧪 测试文件
6. **`test-fixed.js`** - 修正版本测试
7. **`final-comparison.js`** - 最终对比测试
8. **`compare-test.js`** - PHP vs JavaScript 对比

## 🚀 使用方法

### Node.js 环境
```javascript
const CalculateModule = require('./calculate-fixed.js');

const result = CalculateModule.calculate('13800138000');
console.log(result.sn); // A96B62FC7C
```

### 浏览器环境
```html
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
<script src="calculate-browser.js"></script>
<script>
    const result = CalculateUniversal('13800138000');
    console.log(result.sn); // A96B62FC7C
</script>
```

### 服务器部署
```bash
npm install
npm start  # 启动在 http://localhost:3000
```

## 🔒 安全特性

### 代码保护
- **变量名混淆**: 使用十六进制命名
- **字符串加密**: 动态字符码构建
- **函数名混淆**: 核心函数完全混淆
- **UMD 包装**: 支持多种模块系统

### 反调试保护
- **开发者工具检测**: 自动重定向页面
- **调试器检测**: 检测 debugger 语句
- **时间差检测**: 检测调试延迟

## 🧮 算法说明

计算过程（与 PHP 版本完全一致）：

1. **SHA1 哈希**: 对输入手机号进行 SHA1 哈希
2. **截取前20位**: 取 SHA1 结果的前20个字符
3. **MD5 哈希**: 对截取结果进行 MD5 哈希  
4. **最终处理**: 取 MD5 结果的第11-20位并转为大写

## ⚠️ 重要说明

1. **推荐使用**: `calculate-fixed.js` 是修正版本，确保结果正确
2. **浏览器环境**: 必须引入 crypto-js 库
3. **安全提醒**: 虽然代码已混淆，但关键逻辑建议放在服务器端
4. **兼容性**: 支持 Node.js 14+ 和现代浏览器

---

**版权声明**: 此代码仅供授权用户使用，禁止逆向工程和未授权分发。
