// 对比测试 - 验证 JS 版本和 PHP 版本的结果
const CalculateModule = require('./calculate-encrypted.js');

console.log('=== PHP vs JavaScript 结果对比测试 ===\n');

// 测试手机号（与 PHP 版本相同）
const testPhone = '18701879170';
const expectedResult = '1751CB73E9'; // PHP 版本的结果

console.log(`测试手机号: ${testPhone}`);
console.log(`PHP 版本结果: ${expectedResult}`);

try {
    const jsResult = CalculateModule.calculate(testPhone);
    
    if (jsResult.success) {
        console.log(`JavaScript 版本结果: ${jsResult.sn}`);
        console.log(`结果是否一致: ${jsResult.sn === expectedResult ? '✅ 一致' : '❌ 不一致'}`);
        
        if (jsResult.sn !== expectedResult) {
            console.log('\n=== 详细分析 ===');
            console.log('可能的原因:');
            console.log('1. SHA1 算法实现不同');
            console.log('2. MD5 算法实现不同');
            console.log('3. 字符串处理方式不同');
            console.log('4. 编码格式不同');
        }
    } else {
        console.log(`JavaScript 版本错误: ${jsResult.message}`);
    }
} catch (error) {
    console.log(`JavaScript 版本异常: ${error.message}`);
}

// 让我们手动验证算法步骤
console.log('\n=== 手动验证算法步骤 ===');

const crypto = require('crypto');

console.log(`输入: ${testPhone}`);

// 第1步：SHA1哈希
const sha1Result = crypto.createHash('sha1').update(testPhone).digest('hex');
console.log(`SHA1结果: ${sha1Result}`);

// 第2步：截取SHA1前20位
const sha1Substr = sha1Result.substr(0, 20);
console.log(`SHA1前20位: ${sha1Substr}`);

// 第3步：MD5哈希
const md5Result = crypto.createHash('md5').update(sha1Substr).digest('hex');
console.log(`MD5结果: ${md5Result}`);

// 第4步：截取MD5的第11-20位并转大写
const finalResult = md5Result.substr(10, 10).toUpperCase();
console.log(`最终结果: ${finalResult}`);

console.log(`\n与 PHP 结果对比: ${finalResult === expectedResult ? '✅ 一致' : '❌ 不一致'}`);
