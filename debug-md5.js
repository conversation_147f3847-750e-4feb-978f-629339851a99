// 调试 MD5 输入问题
const crypto = require('crypto');

const phone = '13800138000';
console.log(`测试手机号: ${phone}`);

// 第1步：SHA1哈希
const sha1Result = crypto.createHash('sha1').update(phone).digest('hex');
console.log(`SHA1结果: ${sha1Result}`);
console.log(`SHA1长度: ${sha1Result.length}`);

// 第2步：截取SHA1前20位
const sha1Substr = sha1Result.substr(0, 20);
console.log(`SHA1前20位: ${sha1Substr}`);
console.log(`截取长度: ${sha1Substr.length}`);

// 验证截取的内容
console.log('\n=== 验证截取内容 ===');
for (let i = 0; i < 20; i++) {
    console.log(`位置 ${i}: ${sha1Result[i]}`);
}

// 第3步：MD5哈希
const md5Result = crypto.createHash('md5').update(sha1Substr).digest('hex');
console.log(`\nMD5输入: "${sha1Substr}"`);
console.log(`MD5结果: ${md5Result}`);

// 验证 PHP 的预期结果
const expectedMd5 = '5c32ddfbc45f9ac249545ae70f4b47b3';
console.log(`PHP MD5结果: ${expectedMd5}`);
console.log(`MD5是否一致: ${md5Result === expectedMd5}`);

// 手动验证 MD5
console.log('\n=== 手动验证 MD5 ===');
const testInput = 'ffe1cf3289b18e5aedf4';
const testMd5 = crypto.createHash('md5').update(testInput).digest('hex');
console.log(`输入: "${testInput}"`);
console.log(`MD5: ${testMd5}`);

// 检查是否有隐藏字符
console.log('\n=== 检查字符编码 ===');
console.log('SHA1前20位的字符码:');
for (let i = 0; i < sha1Substr.length; i++) {
    console.log(`${i}: "${sha1Substr[i]}" (${sha1Substr.charCodeAt(i)})`);
}
