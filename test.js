// 测试文件 - 验证加密模块功能
const CalculateModule = require('./calculate-encrypted.js');

console.log('=== 万能授权码计算器测试 ===\n');

// 测试用例
const testCases = [
    {
        name: '有效手机号测试 1',
        phone: '13800138000',
        expectValid: true
    },
    {
        name: '有效手机号测试 2', 
        phone: '15912345678',
        expectValid: true
    },
    {
        name: '无效手机号测试 - 长度不足',
        phone: '1380013800',
        expectValid: false
    },
    {
        name: '无效手机号测试 - 长度过长',
        phone: '138001380001',
        expectValid: false
    },
    {
        name: '无效手机号测试 - 开头不正确',
        phone: '12800138000',
        expectValid: false
    },
    {
        name: '无效手机号测试 - 包含字母',
        phone: '1380013800a',
        expectValid: false
    },
    {
        name: '空值测试',
        phone: '',
        expectValid: false
    },
    {
        name: 'null 测试',
        phone: null,
        expectValid: false
    }
];

// 执行验证测试
console.log('1. 手机号验证测试:');
console.log('='.repeat(50));

testCases.forEach((testCase, index) => {
    try {
        const isValid = CalculateModule.validate(testCase.phone);
        const result = isValid === testCase.expectValid ? '✅ 通过' : '❌ 失败';
        
        console.log(`${index + 1}. ${testCase.name}`);
        console.log(`   输入: ${testCase.phone}`);
        console.log(`   期望: ${testCase.expectValid ? '有效' : '无效'}`);
        console.log(`   实际: ${isValid ? '有效' : '无效'}`);
        console.log(`   结果: ${result}\n`);
    } catch (error) {
        console.log(`${index + 1}. ${testCase.name}`);
        console.log(`   输入: ${testCase.phone}`);
        console.log(`   错误: ${error.message}`);
        console.log(`   结果: ❌ 异常\n`);
    }
});

// 执行计算测试
console.log('2. 授权码计算测试:');
console.log('='.repeat(50));

const validPhones = testCases.filter(tc => tc.expectValid).map(tc => tc.phone);

validPhones.forEach((phone, index) => {
    try {
        console.log(`${index + 1}. 计算手机号: ${phone}`);
        
        const result = CalculateModule.calculate(phone);
        
        if (result.success) {
            console.log(`   授权码: ${result.sn}`);
            console.log(`   长度: ${result.sn.length} 字符`);
            console.log(`   格式: ${/^[A-Z0-9]{10}$/.test(result.sn) ? '正确' : '错误'}`);
            console.log(`   结果: ✅ 成功\n`);
        } else {
            console.log(`   错误: ${result.message}`);
            console.log(`   结果: ❌ 失败\n`);
        }
    } catch (error) {
        console.log(`   异常: ${error.message}`);
        console.log(`   结果: ❌ 异常\n`);
    }
});

// 性能测试
console.log('3. 性能测试:');
console.log('='.repeat(50));

const performanceTestPhone = '13800138000';
const iterations = 1000;

console.log(`执行 ${iterations} 次计算测试...`);

const startTime = Date.now();

for (let i = 0; i < iterations; i++) {
    CalculateModule.calculate(performanceTestPhone);
}

const endTime = Date.now();
const totalTime = endTime - startTime;
const avgTime = totalTime / iterations;

console.log(`总耗时: ${totalTime}ms`);
console.log(`平均耗时: ${avgTime.toFixed(2)}ms/次`);
console.log(`处理速度: ${(iterations / (totalTime / 1000)).toFixed(0)} 次/秒\n`);

// 一致性测试
console.log('4. 一致性测试:');
console.log('='.repeat(50));

const consistencyTestPhone = '13800138000';
const results = [];

for (let i = 0; i < 10; i++) {
    const result = CalculateModule.calculate(consistencyTestPhone);
    if (result.success) {
        results.push(result.sn);
    }
}

const allSame = results.every(sn => sn === results[0]);
console.log(`测试手机号: ${consistencyTestPhone}`);
console.log(`计算次数: ${results.length}`);
console.log(`结果一致性: ${allSame ? '✅ 一致' : '❌ 不一致'}`);
console.log(`授权码: ${results[0]}\n`);

console.log('=== 测试完成 ===');

// 导出测试结果供其他模块使用
module.exports = {
    testCases,
    validPhones,
    performanceResult: {
        iterations,
        totalTime: endTime - startTime,
        avgTime: (endTime - startTime) / iterations
    }
};
