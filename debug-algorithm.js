// 详细调试算法步骤
const crypto = require('crypto');

function debugCalculate(phone) {
    console.log(`\n=== 调试计算过程: ${phone} ===`);
    
    // 第1步：SHA1哈希
    const sha1Result = crypto.createHash('sha1').update(phone).digest('hex');
    console.log(`SHA1结果: ${sha1Result}`);
    
    // 第2步：截取SHA1前20位
    const sha1Substr = sha1Result.substr(0, 20);
    console.log(`SHA1前20位: ${sha1Substr}`);
    
    // 第3步：MD5哈希
    const md5Result = crypto.createHash('md5').update(sha1Substr).digest('hex');
    console.log(`MD5结果: ${md5Result}`);
    
    // 第4步：截取MD5的第11-20位并转大写
    const finalResult = md5Result.substr(10, 10).toUpperCase();
    console.log(`最终结果: ${finalResult}`);
    
    return finalResult;
}

// 测试多个手机号
const testPhones = ['18701879170', '13800138000', '15912345678'];

testPhones.forEach(phone => {
    debugCalculate(phone);
});

// 验证 substr 函数的行为
console.log('\n=== 验证 substr 函数 ===');
const testMd5 = '5c32ddfbc45f9ac249545ae70f4b47b3';
console.log(`测试MD5: ${testMd5}`);
console.log(`substr(10, 10): ${testMd5.substr(10, 10)}`);
console.log(`转大写: ${testMd5.substr(10, 10).toUpperCase()}`);

// 验证字符位置
console.log('\n=== 验证字符位置 ===');
for (let i = 0; i < testMd5.length; i++) {
    console.log(`位置 ${i}: ${testMd5[i]}`);
}
