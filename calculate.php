<?php
// header('Content-Type: application/json; charset=utf-8');
// header('Access-Control-Allow-Origin: *');
// header('Access-Control-Allow-Methods: POST');
// header('Access-Control-Allow-Headers: Content-Type');

// // 检查请求方法
// if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
//     http_response_code(405);
//     echo json_encode([
//         'success' => false,
//         'message' => '只支持POST请求'
//     ]);
//     exit;
// }

// // 获取输入参数
// $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';

// // 验证输入
// if (empty($phone)) {
//     echo json_encode([
//         'success' => false,
//         'message' => '手机号不能为空'
//     ]);
//     exit;
// }

// // 验证手机号格式
// if (!isValidPhone($phone)) {
//     echo json_encode([
//         'success' => false,
//         'message' => '请输入正确的手机号格式'
//     ]);
//     exit;
// }

// try {
//     // 调用计算函数
//     $result = calculateUniversalHash($phone);
    
//     // 返回成功结果
//     echo json_encode([
//         'success' => true,
//         'sn' => $result['sn']
//     ]);
    
// } catch (Exception $e) {
//     // 返回错误结果
//     echo json_encode([
//         'success' => false,
//         'message' => '计算过程中发生错误: ' . $e->getMessage()
//     ]);
// }

// /**
//  * 验证手机号格式
//  * @param string $phone 手机号
//  * @return bool 是否为有效手机号
//  */
// function isValidPhone($phone) {
//     // 中国大陆手机号正则表达式
//     // 支持13x, 14x, 15x, 16x, 17x, 18x, 19x开头的11位数字
//     return preg_match('/^1[3-9]\d{9}$/', $phone);
// }

// /**
//  * 计算万能授权码
//  * @param string $universalUsername 万能用户名
//  * @return array 包含哈希值和计算过程的数组
//  */
// function calculateUniversalHash($universalUsername) {
//     // 记录计算过程
//     $process = [];
    
//     // 第1步：SHA1哈希
//     $sha1Result = sha1($universalUsername);
//     $process['sha1'] = $sha1Result;
    
//     // 第2步：截取SHA1前20位
//     $sha1Substr = substr($sha1Result, 0, 20);
//     $process['sha1_substr'] = $sha1Substr;
    
//     // 第3步：MD5哈希
//     $md5Result = md5($sha1Substr);
//     $process['md5'] = $md5Result;
    
//     // 第4步：截取MD5的第11-20位并转大写
//     $universalHash = strtoupper(substr($md5Result, 10, 10));
    
//     return [
//         'sn' => $universalHash,
//         'process' => $process
//     ];
// }

/**
 * 调试版本的计算函数（输出详细过程到控制台）
 * @param string $universalUsername 万能用户名
 * @return string 万能授权码
 */
function calculateUniversalHashDebug($universalUsername) {
    echo "\n=== 万能授权码计算过程 ===\n";
    echo "万能用户名: {$universalUsername}\n";
    
    // 第1步：SHA1哈希
    $sha1Result = sha1($universalUsername);
    echo "SHA1结果: {$sha1Result}\n";
    
    // 第2步：截取SHA1前20位
    $sha1Substr = substr($sha1Result, 0, 20);
    echo "SHA1前20位: {$sha1Substr}\n";
    
    // 第3步：MD5哈希
    $md5Result = md5($sha1Substr);
    echo "MD5结果: {$md5Result}\n";
    
    // 第4步：截取MD5的第11-20位并转大写
    $universalHash = strtoupper(substr($md5Result, 10, 10));
    echo "最终万能授权码: {$universalHash}\n";
    
    return $universalHash;
}


echo calculateUniversalHashDebug('18701879170');
?>
