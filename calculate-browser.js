// 浏览器版本 - 使用 crypto-js 库
(function(global) {
    'use strict';
    
    // 反调试保护
    const _0x1a2b = function() {
        let _0x3c4d = function() {
            return true;
        };
        return _0x3c4d.toString().indexOf('debu' + 'gger') !== -1;
    };
    
    setInterval(function() {
        if (_0x1a2b()) {
            window.location.href = "about:blank";
        }
    }, 1000);
    
    // 检查 crypto-js 是否可用
    if (typeof CryptoJS === 'undefined') {
        throw new Error('需要引入 crypto-js 库');
    }
    
    // 加密的字符串和方法名
    const _0x5e6f = {
        'a': 'sha1',
        'b': 'md5', 
        'c': 'substr',
        'd': 'toUpperCase',
        'e': 'length',
        'f': 'match',
        'g': 'POST',
        'h': 'phone',
        'i': 'success',
        'j': 'message',
        'k': 'sn'
    };
    
    // 动态构建的正则表达式
    const _0x7g8h = new RegExp('^1[3-9]\\d{9}$');
    
    // 加密的哈希函数实现
    function _0x9i0j(input) {
        return CryptoJS.SHA1(input).toString();
    }
    
    function _0xb1c2(input) {
        return CryptoJS.MD5(input).toString();
    }
    
    // 主要计算函数 - 混淆版
    function _0xd3e4(input) {
        try {
            // 步骤1: SHA1
            const _0xf5 = _0x9i0j(input);
            
            // 步骤2: 截取前20位
            const _0xg6 = _0xf5[_0x5e6f.c](0, 20);
            
            // 步骤3: MD5
            const _0xh7 = _0xb1c2(_0xg6);
            
            // 步骤4: 截取第11-20位并转大写
            const _0xi8 = _0xh7[_0x5e6f.c](10, 10)[_0x5e6f.d]();
            
            return {
                [_0x5e6f.i]: true,
                [_0x5e6f.k]: _0xi8
            };
        } catch (e) {
            return {
                [_0x5e6f.i]: false,
                [_0x5e6f.j]: '计算过程中发生错误'
            };
        }
    }
    
    // 手机号验证函数 - 混淆版
    function _0xj9k0(phone) {
        if (!phone || phone[_0x5e6f.e] !== 11) {
            return false;
        }
        return _0x7g8h.test(phone);
    }
    
    // 全局函数
    global[String.fromCharCode(67,97,108,99,117,108,97,116,101,85,110,105,118,101,114,115,97,108)] = function(phone) {
        if (!phone) {
            return {
                [_0x5e6f.i]: false,
                [_0x5e6f.j]: '手机号不能为空'
            };
        }
        
        if (!_0xj9k0(phone)) {
            return {
                [_0x5e6f.i]: false,
                [_0x5e6f.j]: '请输入正确的手机号格式'
            };
        }
        
        return _0xd3e4(phone);
    };
    
    // 验证函数
    global[String.fromCharCode(86,97,108,105,100,97,116,101,80,104,111,110,101)] = _0xj9k0;
    
})(typeof window !== 'undefined' ? window : this);
