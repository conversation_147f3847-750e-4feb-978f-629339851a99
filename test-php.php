<?php
// 简单的 PHP 测试
$phone = '13800138000';
echo "测试手机号: $phone\n";

// 第1步：SHA1哈希
$sha1Result = sha1($phone);
echo "SHA1结果: $sha1Result\n";

// 第2步：截取SHA1前20位
$sha1Substr = substr($sha1Result, 0, 20);
echo "SHA1前20位: $sha1Substr\n";

// 第3步：MD5哈希
$md5Result = md5($sha1Substr);
echo "MD5结果: $md5Result\n";

// 第4步：截取MD5的第11-20位并转大写
$finalResult = strtoupper(substr($md5Result, 10, 10));
echo "最终结果: $finalResult\n";

// 验证输入
echo "\n=== 验证 ===\n";
echo "MD5输入: '$sha1Substr'\n";
echo "MD5输入长度: " . strlen($sha1Substr) . "\n";

// 测试另一个手机号
echo "\n=== 测试另一个手机号 ===\n";
$phone2 = '15912345678';
echo "手机号: $phone2\n";
$sha1_2 = sha1($phone2);
$sha1_substr_2 = substr($sha1_2, 0, 20);
$md5_2 = md5($sha1_substr_2);
$final_2 = strtoupper(substr($md5_2, 10, 10));
echo "结果: $final_2\n";
?>
