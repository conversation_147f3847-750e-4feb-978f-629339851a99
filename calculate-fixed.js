// 修正版加密计算模块 - 确保与 PHP 版本结果一致
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).CalculateModule=t()}(this,function(){"use strict";
    
    // 动态解密密钥
    const _0x4f8a = [0x73,0x68,0x61,0x31]; // 'sha1'
    const _0x6b2c = [0x6d,0x64,0x35]; // 'md5'
    const _0x8d4e = [0x73,0x75,0x62,0x73,0x74,0x72]; // 'substr'
    const _0x0f6g = [0x74,0x6f,0x55,0x70,0x70,0x65,0x72,0x43,0x61,0x73,0x65]; // 'toUpperCase'
    
    // 反调试和反分析保护
    const _0x2h8i = function(){
        const _0x4j0k = function(){return !0};
        return _0x4j0k.toString().indexOf(String.fromCharCode(100,101,98,117,103,103,101,114)) !== -1;
    };
    
    // 动态检测开发者工具
    let _0x6l2m = !1;
    setInterval(function(){
        const _0x8n4o = new Date();
        debugger;
        if(new Date() - _0x8n4o > 100) _0x6l2m = !0;
        if(_0x2h8i() || _0x6l2m) {
            if (typeof window !== 'undefined') {
                window.location.href = "about:blank";
            }
        }
    }, 500);
    
    // 加密的字符串解码函数
    function _0x0p6q(arr) {
        return String.fromCharCode.apply(null, arr);
    }
    
    // 动态导入crypto模块 (Node.js环境)
    let _0x2r8s = null;
    try {
        _0x2r8s = eval('require')('crypto');
    } catch(e) {
        // 浏览器环境，需要crypto-js库
    }
    
    // 正确的SHA1实现
    function _0x4t0u(str) {
        if (_0x2r8s) {
            // Node.js 环境使用内置 crypto
            return _0x2r8s.createHash(_0x0p6q(_0x4f8a)).update(str).digest('hex');
        } else {
            // 浏览器环境需要引入crypto-js库
            if (typeof CryptoJS !== 'undefined') {
                return CryptoJS.SHA1(str).toString();
            } else {
                throw new Error('浏览器环境需要引入 crypto-js 库');
            }
        }
    }
    
    // 正确的MD5实现
    function _0x8x4y(str) {
        if (_0x2r8s) {
            // Node.js 环境使用内置 crypto
            return _0x2r8s.createHash(_0x0p6q(_0x6b2c)).update(str).digest('hex');
        } else {
            // 浏览器环境需要引入crypto-js库
            if (typeof CryptoJS !== 'undefined') {
                return CryptoJS.MD5(str).toString();
            } else {
                throw new Error('浏览器环境需要引入 crypto-js 库');
            }
        }
    }
    
    // 核心计算函数 - 高度混淆但算法正确
    const _0x2b8c = function(input) {
        // 输入验证
        if (!input || typeof input !== 'string') {
            return {
                success: false,
                message: '输入参数无效'
            };
        }
        
        try {
            // 步骤1: SHA1哈希
            const _0x4d0e = _0x4t0u(input);
            
            // 步骤2: 截取SHA1前20位
            const _0x6f2g = _0x4d0e[_0x0p6q(_0x8d4e)](0, 20);
            
            // 步骤3: MD5哈希
            const _0x8h4i = _0x8x4y(_0x6f2g);
            
            // 步骤4: 截取MD5的第11-20位并转大写
            const _0x0j6k = _0x8h4i[_0x0p6q(_0x8d4e)](10, 10)[_0x0p6q(_0x0f6g)]();
            
            return {
                success: true,
                sn: _0x0j6k
            };
        } catch (e) {
            return {
                success: false,
                message: '计算过程中发生错误: ' + e.message
            };
        }
    };
    
    // 手机号验证 - 混淆版
    const _0x2l8m = function(phone) {
        if (!phone) return false;
        const _0x4n0o = /^1[3-9]\d{9}$/;
        return _0x4n0o.test(phone) && phone.length === 11;
    };
    
    // 主要导出函数
    const _0x6p2q = {
        // 计算函数
        [String.fromCharCode(99,97,108,99,117,108,97,116,101)]: function(phone) {
            if (!_0x2l8m(phone)) {
                return {
                    success: false,
                    message: '请输入正确的手机号格式'
                };
            }
            return _0x2b8c(phone);
        },
        
        // 验证函数
        [String.fromCharCode(118,97,108,105,100,97,116,101)]: _0x2l8m,
        
        // Express中间件 (Node.js环境)
        [String.fromCharCode(109,105,100,100,108,101,119,97,114,101)]: function() {
            return function(req, res, next) {
                if (req.method !== 'POST') {
                    return res.status(405).json({
                        success: false,
                        message: '只支持POST请求'
                    });
                }
                
                const phone = req.body.phone || '';
                
                if (!phone) {
                    return res.json({
                        success: false,
                        message: '手机号不能为空'
                    });
                }
                
                const result = _0x6p2q.calculate(phone);
                res.json(result);
            };
        }
    };
    
    // 浏览器环境全局注册
    if (typeof window !== 'undefined') {
        window.CalculateUniversal = _0x6p2q.calculate;
        window.ValidatePhone = _0x6p2q.validate;
    }
    
    return _0x6p2q;
});
