// Node.js 服务器版本 - 使用加密的计算模块
const express = require('express');
const path = require('path');
const CalculateModule = require('./calculate-encrypted.js');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件设置
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS 设置
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// 静态文件服务
app.use(express.static(path.join(__dirname)));

// API 路由 - 计算授权码
app.post('/api/calculate', (req, res) => {
    try {
        const { phone } = req.body;
        
        if (!phone) {
            return res.status(400).json({
                success: false,
                message: '手机号不能为空'
            });
        }
        
        // 使用加密模块进行计算
        const result = CalculateModule.calculate(phone);
        
        // 记录访问日志（生产环境中应该使用专业的日志系统）
        console.log(`[${new Date().toISOString()}] 计算请求 - 手机号: ${phone.substring(0, 3)}****${phone.substring(7)} - 结果: ${result.success ? '成功' : '失败'}`);
        
        res.json(result);
        
    } catch (error) {
        console.error('计算过程中发生错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 验证手机号 API
app.post('/api/validate', (req, res) => {
    try {
        const { phone } = req.body;
        
        const isValid = CalculateModule.validate(phone);
        
        res.json({
            success: true,
            valid: isValid,
            message: isValid ? '手机号格式正确' : '手机号格式不正确'
        });
        
    } catch (error) {
        console.error('验证过程中发生错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'usage-example.html'));
});

// 404 处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: '请求的资源不存在'
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);
    res.status(500).json({
        success: false,
        message: '服务器内部错误'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器已启动，端口: ${PORT}`);
    console.log(`访问地址: http://localhost:${PORT}`);
    console.log(`API 端点:`);
    console.log(`  POST /api/calculate - 计算授权码`);
    console.log(`  POST /api/validate - 验证手机号`);
    console.log(`  GET  /health - 健康检查`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到 SIGTERM 信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('收到 SIGINT 信号，正在关闭服务器...');
    process.exit(0);
});

module.exports = app;
