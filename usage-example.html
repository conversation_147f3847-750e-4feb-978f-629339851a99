<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>万能授权码计算器 - JavaScript版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input[type="text"]:focus {
            border-color: #4CAF50;
            outline: none;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .sn-display {
            font-size: 24px;
            text-align: center;
            letter-spacing: 2px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>万能授权码计算器</h1>
        <form id="calculateForm">
            <div class="form-group">
                <label for="phone">手机号码：</label>
                <input type="text" id="phone" name="phone" placeholder="请输入11位手机号码" maxlength="11">
            </div>
            <button type="submit">计算授权码</button>
        </form>
        
        <div id="result" style="display: none;"></div>
    </div>

    <!-- 引入加密的计算模块 -->
    <script src="calculate-encrypted.js"></script>
    
    <script>
        // 防止右键和F12
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        document.addEventListener('keydown', function(e) {
            // 禁用F12, Ctrl+Shift+I, Ctrl+U等
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });
        
        // 表单提交处理
        document.getElementById('calculateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!phone) {
                showResult('手机号不能为空', false);
                return;
            }
            
            try {
                // 调用加密的计算函数
                const result = CalculateUniversal(phone);
                
                if (result.success) {
                    showResult(`授权码计算成功！<div class="sn-display">${result.sn}</div>`, true);
                } else {
                    showResult(result.message, false);
                }
            } catch (error) {
                showResult('计算过程中发生错误，请稍后重试', false);
            }
        });
        
        // 显示结果
        function showResult(message, isSuccess) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.style.display = 'block';
        }
        
        // 手机号输入格式化
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // 只保留数字
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            e.target.value = value;
        });
    </script>
</body>
</html>
