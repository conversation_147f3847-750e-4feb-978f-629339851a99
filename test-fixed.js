// 测试修正版本
const CalculateModule = require('./calculate-fixed.js');

console.log('=== 修正版本测试 ===\n');

// 测试用例 - 与 PHP 版本对比
const testCases = [
    {
        phone: '18701879170',
        expected: '1751CB73E9'  // PHP 版本的结果
    },
    {
        phone: '13800138000',
        expected: null  // 需要用 PHP 验证
    },
    {
        phone: '15912345678',
        expected: null  // 需要用 PHP 验证
    }
];

testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. 测试手机号: ${testCase.phone}`);
    
    try {
        const result = CalculateModule.calculate(testCase.phone);
        
        if (result.success) {
            console.log(`   计算结果: ${result.sn}`);
            
            if (testCase.expected) {
                const isMatch = result.sn === testCase.expected;
                console.log(`   期望结果: ${testCase.expected}`);
                console.log(`   结果对比: ${isMatch ? '✅ 一致' : '❌ 不一致'}`);
            } else {
                console.log(`   状态: ✅ 计算成功`);
            }
        } else {
            console.log(`   错误: ${result.message}`);
        }
    } catch (error) {
        console.log(`   异常: ${error.message}`);
    }
    
    console.log('');
});

// 验证算法步骤
console.log('=== 验证算法步骤 (手机号: 18701879170) ===');
const crypto = require('crypto');
const testPhone = '18701879170';

console.log(`输入: ${testPhone}`);

// 第1步：SHA1哈希
const sha1Result = crypto.createHash('sha1').update(testPhone).digest('hex');
console.log(`SHA1结果: ${sha1Result}`);

// 第2步：截取SHA1前20位
const sha1Substr = sha1Result.substr(0, 20);
console.log(`SHA1前20位: ${sha1Substr}`);

// 第3步：MD5哈希
const md5Result = crypto.createHash('md5').update(sha1Substr).digest('hex');
console.log(`MD5结果: ${md5Result}`);

// 第4步：截取MD5的第11-20位并转大写
const finalResult = md5Result.substr(10, 10).toUpperCase();
console.log(`最终结果: ${finalResult}`);

console.log(`\n修正版本结果: ${CalculateModule.calculate(testPhone).sn}`);
console.log(`算法验证: ${finalResult === CalculateModule.calculate(testPhone).sn ? '✅ 一致' : '❌ 不一致'}`);
