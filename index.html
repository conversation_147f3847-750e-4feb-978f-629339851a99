<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>万能授权码计算器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="tel"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }

        input[type="text"]:focus, input[type="tel"]:focus {
            border-color: #007bff;
            outline: none;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .result h3 {
            margin-top: 0;
            color: #333;
        }
        
        .hash-code {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            background: white;
            padding: 10px;
            border-radius: 3px;
            border: 1px solid #ddd;
            text-align: center;
            margin: 10px 0;
        }
        
        .process {
            margin-top: 15px;
            font-size: 14px;
            color: #666;
        }
        
        .process-step {
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-radius: 3px;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #007bff;
        }
        
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border-color: #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>万能授权码计算器</h1>
        
        <form id="hashForm">
            <div class="input-group">
                <label for="phone">手机号:</label>
                <input type="tel" id="phone" name="phone" placeholder="请输入11位手机号" pattern="1[3-9]\d{9}" maxlength="11" required>
            </div>

            <button type="submit" class="btn" id="calculateBtn">计算万能授权码</button>
        </form>
        
        <div class="loading" id="loading">
            <p>正在计算中...</p>
        </div>
        
        <div id="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('hashForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value.trim();
            if (!phone) {
                alert('请输入手机号');
                return;
            }

            // 验证手机号格式
            if (!isValidPhone(phone)) {
                alert('请输入正确的手机号格式');
                return;
            }

            calculateHash(phone);
        });

        // 验证手机号格式
        function isValidPhone(phone) {
            // 中国大陆手机号正则表达式
            // 支持13x, 14x, 15x, 16x, 17x, 18x, 19x开头的11位数字
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        function calculateHash(phone) {
            const loadingDiv = document.getElementById('loading');
            const resultDiv = document.getElementById('result');
            const calculateBtn = document.getElementById('calculateBtn');
            
            // 显示加载状态
            loadingDiv.style.display = 'block';
            resultDiv.style.display = 'none';
            calculateBtn.disabled = true;
            
            // 创建AJAX请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'calculate.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    loadingDiv.style.display = 'none';
                    calculateBtn.disabled = false;
                    
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            displayResult(response);
                        } catch (e) {
                            displayError('服务器返回数据格式错误');
                        }
                    } else {
                        displayError('请求失败，请检查服务器连接');
                    }
                }
            };
            
            xhr.onerror = function() {
                loadingDiv.style.display = 'none';
                calculateBtn.disabled = false;
                displayError('网络连接错误');
            };
            
            // 发送请求
            xhr.send('phone=' + encodeURIComponent(phone));
        }
        
        function displayResult(data) {
            const resultDiv = document.getElementById('result');

            if (data.success) {
                resultDiv.innerHTML = `
                    <div class="result">
                        <h3>计算结果</h3>
                        <div class="hash-code">${data.sn}</div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        错误: ${data.message}
                    </div>
                `;
            }

            resultDiv.style.display = 'block';
        }
        
        function displayError(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="error">
                    ${message}
                </div>
            `;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
